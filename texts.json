{"game": {"title": "FintechFarm Chess", "status": {"yourTurn": "Your turn", "aiThinking": "AI thinking...", "checkYourTurn": "Check! Your turn", "checkAiThinking": "Check! AI thinking...", "checkmateYouWon": "Checkmate! You won!", "checkmateYouLost": "Checkmate! You lost.", "stalemate": "Stalemate - Draw!"}, "buttons": {"newGame": "New Game", "soundOn": "Sound On", "soundOff": "Sound Off"}, "victory": {"title": "Happy Birthday!"}, "errors": {"chessNotLoaded": "Chess.js library not loaded", "noNotificationTexts": "No notification texts available", "soundPlayFailed": "Sound play failed", "textsLoadFailed": "Failed to load texts"}}, "notifications": ["Мне нравится", "Хватит в размеренном темпе жить", "this is f...ng amazing result", "Простите, мне не нравится", "Очень скучно, но да ладно", "Бесконечное ожидание", "Мамочки", "На все рынки, пожалуйста", "Спасибо большое. Идите спать, пожалуйста", "Никаких дилдо!", "Секс и депозиты", "Музыкой навеяло?", "Напомните, пожалуйста, какой у нас статус?", "Что-то мешает уже начать разработку?", "Не осознавал..."], "pieces": {"K": "🐄", "Q": "🦅", "R": "🐋", "B": "🦓", "N": "🐪", "P": "🐣", "k": "🐄", "q": "🦅", "r": "🐋", "b": "🦓", "n": "🐪", "p": "🐣"}, "colors": {"light": "#F0D9B5", "dark": "#B58863"}}