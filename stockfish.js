/*! * Stockfish copyright <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> * and other contributors. * * Multi-variant support by <PERSON> and contributors: * https://github.com/ddu<PERSON><PERSON>/Stockfish * * Released under the GNU General Public License v3. * * Compiled to JavaScript and Webassembly by <PERSON><PERSON> * using Emscripten and Binaryen. * * https://github.com/niklasf/stockfish.js */ var Module=typeof Module!=="undefined"?Module:{};Module=function(){var queue=[];onmessage=function(e){if(e.data=="quit")close();else if(queue!==null)queue.push(e.data);else Module.ccall("uci_command","number",["string"],[e.data])};return{locateFile:function(file){return file},print:function(stdout){postMessage(stdout)},postRun:function(){for(var i=0;i<queue.length;i++)Module.ccall("uci_command","number",["string"],[queue[i]]);queue=null}}}();
